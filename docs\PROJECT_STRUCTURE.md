# Vierla Project Structure

This document outlines the organization and structure of the Vierla project.

## Directory Structure

```
vierla/
├── app/                          # Next.js App Router pages
│   ├── about/                    # About page
│   │   └── page.tsx             # About page component
│   ├── globals.css              # Global styles and CSS variables
│   ├── layout.tsx               # Root layout component
│   └── page.tsx                 # Homepage component
├── components/                   # Reusable React components
│   ├── ui/                      # UI component library (shadcn/ui)
│   │   ├── accordion.tsx        # Accordion component
│   │   ├── alert-dialog.tsx     # Alert dialog component
│   │   ├── button.tsx           # Button component
│   │   ├── card.tsx             # Card component
│   │   ├── toast.tsx            # Toast notification component
│   │   ├── toaster.tsx          # Toast container component
│   │   └── ...                  # Other UI components
│   └── theme-provider.tsx       # Theme context provider
├── hooks/                       # Custom React hooks
│   ├── use-mobile.tsx           # Mobile detection hook
│   └── use-toast.ts             # Toast notification hook
├── lib/                         # Utility libraries
│   └── utils.ts                 # Utility functions (cn, etc.)
├── public/                      # Static assets
│   ├── placeholder-logo.png     # Logo placeholder
│   ├── placeholder-logo.svg     # SVG logo placeholder
│   └── ...                      # Other static assets
├── docs/                        # Documentation
│   ├── README.md                # Documentation index
│   ├── PROJECT_STRUCTURE.md     # This file
│   ├── DEPLOYMENT.md            # Deployment guide
│   ├── PROJECT_STATUS.md        # Project status report
│   ├── CHANGES_SUMMARY.md       # Change log
│   └── Website Enhancement Research Document_.md # Design research
├── config/                      # Configuration files
│   └── nginx-vierla.conf        # Nginx configuration template
├── README.md                    # Main project documentation
├── docker-compose.yml           # Docker Compose configuration
├── Dockerfile                   # Docker build configuration
├── deploy.sh                    # Deployment script
├── package.json                 # Node.js dependencies and scripts
├── tailwind.config.ts           # Tailwind CSS configuration
├── tsconfig.json                # TypeScript configuration
├── next.config.mjs              # Next.js configuration
├── postcss.config.mjs           # PostCSS configuration
└── components.json              # shadcn/ui configuration
```

## File Naming Conventions

### Components
- **React Components**: PascalCase with `.tsx` extension
  - `Button.tsx`, `Card.tsx`, `ThemeProvider.tsx`
- **Page Components**: lowercase with `.tsx` extension
  - `page.tsx`, `layout.tsx`

### Hooks
- **Custom Hooks**: kebab-case with `use-` prefix
  - `use-mobile.tsx`, `use-toast.ts`

### Utilities
- **Utility Files**: kebab-case with `.ts` extension
  - `utils.ts`

### Configuration
- **Config Files**: kebab-case or standard names
  - `tailwind.config.ts`, `next.config.mjs`, `package.json`

### Documentation
- **Documentation**: UPPERCASE with `.md` extension
  - `README.md`, `DEPLOYMENT.md`, `PROJECT_STATUS.md`

## Import Conventions

### Path Aliases
Configured in `tsconfig.json` and `components.json`:

```typescript
// Components
import { Button } from "@/components/ui/button"
import { ThemeProvider } from "@/components/theme-provider"

// Hooks
import { useIsMobile } from "@/hooks/use-mobile"
import { useToast } from "@/hooks/use-toast"

// Utilities
import { cn } from "@/lib/utils"
```

### Import Order
1. React and Next.js imports
2. Third-party library imports
3. Internal component imports
4. Internal hook imports
5. Internal utility imports
6. Type imports (with `type` keyword)

## Component Organization

### UI Components (`components/ui/`)
- Atomic design principles
- Reusable across the application
- Based on shadcn/ui component library
- Consistent styling with Tailwind CSS

### Page Components (`app/`)
- Next.js App Router structure
- Each directory represents a route
- `page.tsx` files are route components
- `layout.tsx` files provide shared layouts

### Custom Hooks (`hooks/`)
- Reusable stateful logic
- Follow React hooks conventions
- Properly typed with TypeScript

## Styling Architecture

### Tailwind CSS
- Utility-first CSS framework
- Custom color scheme defined in `tailwind.config.ts`
- CSS variables for theme consistency

### Global Styles
- Located in `app/globals.css`
- Contains Tailwind directives
- CSS custom properties for theming
- Base layer styles

## Configuration Files

### Next.js (`next.config.mjs`)
- Standalone output for Docker
- Image optimization settings
- Build configuration

### TypeScript (`tsconfig.json`)
- Strict type checking
- Path aliases configuration
- Modern ES features enabled

### Tailwind (`tailwind.config.ts`)
- Custom color palette
- Component content paths
- Plugin configurations

### Docker (`docker-compose.yml`, `Dockerfile`)
- Multi-stage build process
- Production optimization
- Health check configuration

## Development Workflow

### Local Development
```bash
npm run dev          # Start development server
npm run build        # Build for production
npm run start        # Start production server
npm run lint         # Run ESLint
```

### Deployment
```bash
./deploy.sh          # Automated deployment script
```

## Code Quality Standards

### TypeScript
- Strict mode enabled
- Proper type definitions
- Interface over type aliases for objects

### Component Standards
- Functional components with hooks
- Proper prop typing
- Forward refs where appropriate
- Consistent naming conventions

### Styling Standards
- Tailwind utility classes
- Responsive design patterns
- Consistent spacing and typography
- Accessible color contrasts

## Dependencies Management

### Production Dependencies
- React 19 and Next.js 15
- Tailwind CSS for styling
- Radix UI for accessible components
- Lucide React for icons

### Development Dependencies
- TypeScript for type safety
- ESLint for code quality
- PostCSS for CSS processing

This structure ensures maintainability, scalability, and consistency across the Vierla project.
