"use client"
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, Heart, ArrowLeft, Target, Eye, Clock } from "lucide-react"
import Link from "next/link"

import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"

export default function AboutPage() {
  return (
    <div className="min-h-screen relative overflow-hidden">
      {/* Professional Background with <PERSON> Sage Green */}
      <div className="absolute inset-0" style={{background: 'linear-gradient(to bottom right, #5A7A63, #4a6b54, #5A7A63)'}}>
        <div className="absolute inset-0 animate-pulse" style={{background: 'linear-gradient(to top right, rgba(90, 122, 99, 0.3), rgba(74, 107, 84, 0.3), rgba(90, 122, 99, 0.3))'}} />

        {/* Floating Blobs */}
        {[...Array(6)].map((_, i) => (
          <div
            key={i}
            className="absolute rounded-full bg-white/10 backdrop-blur-sm animate-float"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              width: `${Math.random() * 300 + 100}px`,
              height: `${Math.random() * 300 + 100}px`,
              animationDelay: `${Math.random() * 5}s`,
              animationDuration: `${Math.random() * 10 + 15}s`,
            }}
          />
        ))}
      </div>

      {/* Header */}
      <header className="relative z-10 container mx-auto px-4 py-6">
        <nav className="flex items-center justify-between">
          <div className="flex items-center space-x-3 group">
            <div className="w-12 h-12 bg-white/20 backdrop-blur-md rounded-2xl flex items-center justify-center shadow-lg border border-white/30 transform group-hover:scale-110 transition-all duration-300">
              <Sparkles className="w-7 h-7 text-white" />
            </div>
            <div>
              <span className="text-3xl font-bold text-white drop-shadow-lg">Vierla</span>
              <div className="text-xs text-white/80 font-medium">EXPERT BEAUTY SERVICES, DELIVERED TO YOU</div>
            </div>
          </div>
          <div className="flex items-center space-x-8">
            <Link
              href="/"
              className="flex items-center text-white/90 hover:text-white transition-all duration-300 font-medium transform hover:scale-110 drop-shadow-sm"
            >
              <ArrowLeft className="w-5 h-5 mr-2" />
              Back to Home
            </Link>

          </div>
        </nav>
      </header>

      {/* Hero Section */}
      <section className="relative z-10 container mx-auto px-4 py-16">
        <div className="text-center max-w-4xl mx-auto mb-20">
          <h1 className="text-5xl md:text-7xl font-black mb-8 leading-none text-white drop-shadow-lg" style={{fontFamily: 'Playfair Display, serif'}}>
            <span className="inline-block">About</span>
            <br />
            <span className="inline-block" style={{color: '#F5F0E1'}}>
              Vierla
            </span>
          </h1>
          <p className="text-xl text-white/90 leading-relaxed font-light drop-shadow-sm max-w-4xl mx-auto">
            Building the future of beauty services through trust, quality, and convenience. We're creating a curated marketplace that connects discerning customers with top-tier beauty professionals.
          </p>
        </div>
      </section>

      {/* Our Story Section */}
      <section className="relative z-10 py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="grid lg:grid-cols-2 gap-16 items-center mb-20">
              <div className="bg-white/20 backdrop-blur-md rounded-3xl p-12 shadow-2xl border border-white/30">
                <h2 className="text-5xl font-bold text-white mb-8 drop-shadow-sm">Our Story</h2>
                <p className="text-white/90 text-lg leading-relaxed mb-6 drop-shadow-sm">
                  Founded in 2025, Vierla emerged from a vision to revolutionize how beauty service providers connect with customers.
                  We recognized the need for a comprehensive platform that empowers providers while simplifying discovery and booking for customers.
                </p>
                <p className="text-white/90 text-lg leading-relaxed mb-6 drop-shadow-sm">
                  Our platform gives service providers their own digital stores where they can showcase their work, manage bookings,
                  access business analytics, and process payments. For customers, we provide a centralized location to discover beauty
                  and self-care services, view portfolios, book appointments, message providers, and make secure payments.
                </p>
                <p className="text-white/90 text-lg leading-relaxed drop-shadow-sm">
                  We're building an ecosystem that benefits both sides of the beauty industry - helping providers grow their businesses
                  while giving customers easy access to quality beauty and self-care services in their area.
                </p>
              </div>

              <div className="space-y-8">
                {[
                  {
                    icon: Award,
                    title: "Excellence First",
                    description:
                      "Every professional in our network is certified, background-checked, and committed to delivering exceptional results.",
                  },
                  {
                    icon: Shield,
                    title: "Trust & Safety",
                    description:
                      "Your safety is our priority. All our professionals are insured and follow strict health and safety protocols.",
                  },
                  {
                    icon: Zap,
                    title: "Innovation Driven",
                    description:
                      "We leverage cutting-edge technology to provide seamless booking, real-time tracking, and personalized experiences.",
                  },
                  {
                    icon: Heart,
                    title: "Customer Centric",
                    description:
                      "Every decision we make is guided by our commitment to enhancing your beauty and wellness journey.",
                  },
                ].map((value, index) => (
                  <div
                    key={index}
                    className="bg-white/10 backdrop-blur-md rounded-2xl p-6 shadow-lg border border-white/20 transform hover:scale-105 transition-all duration-300"
                  >
                    <div className="flex items-start space-x-4">
                      <div className="w-12 h-12 bg-white/20 backdrop-blur-md rounded-full flex items-center justify-center border border-white/30">
                        <value.icon className="w-6 h-6 text-white" />
                      </div>
                      <div>
                        <h4 className="text-xl font-bold text-white mb-2 drop-shadow-sm">{value.title}</h4>
                        <p className="text-white/80 leading-relaxed drop-shadow-sm">{value.description}</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Vision & Mission Section */}
      <section className="relative z-10 py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-6xl font-black text-white mb-6 drop-shadow-lg">Vision & Mission</h2>
              <p className="text-2xl text-white/90 max-w-3xl mx-auto drop-shadow-sm">
                Guiding principles that shape our commitment to beauty and wellness
              </p>
            </div>

            <div className="grid lg:grid-cols-2 gap-12 mb-20">
              <div className="bg-white/20 backdrop-blur-md rounded-3xl p-12 shadow-2xl border border-white/30 transform hover:scale-105 transition-all duration-500">
                <div className="flex items-center mb-8">
                  <div className="w-16 h-16 bg-white/20 backdrop-blur-md rounded-full flex items-center justify-center mr-6 border border-white/30">
                    <Eye className="w-8 h-8 text-white" />
                  </div>
                  <h3 className="text-4xl font-bold text-white drop-shadow-sm">Our Vision</h3>
                </div>
                <p className="text-white/90 text-lg leading-relaxed drop-shadow-sm">
                  To become the world's leading marketplace connecting beauty service providers and customers, empowering providers
                  with comprehensive business tools while giving customers seamless access to quality beauty and self-care services.
                </p>
              </div>

              <div className="bg-white/20 backdrop-blur-md rounded-3xl p-12 shadow-2xl border border-white/30 transform hover:scale-105 transition-all duration-500">
                <div className="flex items-center mb-8">
                  <div className="w-16 h-16 bg-white/20 backdrop-blur-md rounded-full flex items-center justify-center mr-6 border border-white/30">
                    <Target className="w-8 h-8 text-white" />
                  </div>
                  <h3 className="text-4xl font-bold text-white drop-shadow-sm">Our Mission</h3>
                </div>
                <p className="text-white/90 text-lg leading-relaxed drop-shadow-sm">
                  To revolutionize the beauty industry by providing service providers with powerful business management tools
                  including digital stores, booking systems, analytics, and payment processing, while offering customers a
                  centralized platform to discover, book, and pay for beauty and self-care services.
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Why Choose Us Section */}
      <section className="relative z-10 py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-5xl font-black text-white mb-6 drop-shadow-lg">Why Choose Vierla?</h2>
              <p className="text-xl text-white/90 drop-shadow-sm">What sets us apart in the beauty industry</p>
            </div>

            <div className="grid md:grid-cols-3 gap-8">
              {[
                {
                  icon: Clock,
                  title: "Convenience",
                  description:
                    "Book services at your preferred time and location. No more waiting in salons or traveling across town.",
                },
                {
                  icon: Shield,
                  title: "Quality Assurance",
                  description:
                    "All professionals are vetted, certified, and regularly reviewed to maintain our high standards.",
                },
                {
                  icon: Heart,
                  title: "Personalized Care",
                  description:
                    "Each service is tailored to your unique needs and preferences for the best possible results.",
                },
              ].map((feature, index) => (
                <Card
                  key={index}
                  className="bg-white/15 backdrop-blur-md border border-white/25 shadow-xl hover:shadow-2xl transition-all duration-500 hover:-translate-y-2 rounded-2xl overflow-hidden group cursor-pointer transform hover:scale-105"
                >
                  <CardContent className="p-8 text-center">
                    <div className="w-16 h-16 bg-white/20 backdrop-blur-md rounded-full flex items-center justify-center mx-auto mb-6 shadow-lg border border-white/30">
                      <feature.icon className="w-8 h-8 text-white group-hover:animate-bounce transition-all duration-300 drop-shadow-sm" />
                    </div>
                    <h3 className="text-2xl font-bold text-white mb-4 group-hover:scale-110 transition-all duration-300 drop-shadow-sm">
                      {feature.title}
                    </h3>
                    <p className="text-white/80 leading-relaxed group-hover:text-white transition-colors duration-300 drop-shadow-sm">
                      {feature.description}
                    </p>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </div>
      </section>



      {/* Footer */}
      <footer className="relative z-10 bg-black/20 backdrop-blur-md text-white py-16 border-t border-white/20">
        <div className="container mx-auto px-4">
          <div className="text-center">
            <div className="flex items-center justify-center space-x-3 mb-6">
              <div className="w-10 h-10 bg-white/20 backdrop-blur-md rounded-2xl flex items-center justify-center border border-white/30">
                <Sparkles className="w-6 h-6 text-white drop-shadow-sm" />
              </div>
              <span className="text-3xl font-bold drop-shadow-lg">Vierla</span>
            </div>
            <p className="text-white/80 drop-shadow-sm">
              &copy; 2024 Vierla - Your Self-Care, Simplified. All rights reserved.
            </p>
          </div>
        </div>
      </footer>

      <style jsx>{`
        @keyframes float {
          0%, 100% { transform: translateY(0px) rotate(0deg); }
          25% { transform: translateY(-20px) rotate(2deg); }
          50% { transform: translateY(-40px) rotate(0deg); }
          75% { transform: translateY(-20px) rotate(-2deg); }
        }
        .animate-float {
          animation: float 20s ease-in-out infinite;
        }
      `}</style>
    </div>
  )
}
