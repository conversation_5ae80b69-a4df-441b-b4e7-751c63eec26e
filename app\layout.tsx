import type { Metadata } from 'next'
import './globals.css'

export const metadata: Metadata = {
  title: 'Vierla - Expert Beauty Services, Delivered to You',
  description: 'Book top-vetted, local hair stylists, makeup artists, and nail technicians with confidence. Your place, your time.',
  generator: 'Next.js',
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <html lang="en">
      <head>
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
        <link href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;700;900&family=Manrope:wght@300;400;500;600;700&display=swap" rel="stylesheet" />
      </head>
      <body style={{fontFamily: 'Manrope, sans-serif'}}>{children}</body>
    </html>
  )
}
