version: '3.8'

services:
  vierla-web:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: vierla-web
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - PORT=3000
    volumes:
      - ./logs:/app/logs
    networks:
      - vierla-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

networks:
  vierla-network:
    driver: bridge

volumes:
  logs:
    driver: local
