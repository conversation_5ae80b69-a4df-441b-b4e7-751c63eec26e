# Documentation Index

This directory contains all documentation for the Vierla project.

## Available Documentation

- **[Project Overview](../README.md)** - Main project information and setup
- **[Deployment Guide](./DEPLOYMENT.md)** - Complete deployment instructions
- **[Project Status](./PROJECT_STATUS.md)** - Current status and features
- **[Changes Summary](./CHANGES_SUMMARY.md)** - Detailed change log
- **[Website Enhancement Research](./Website%20Enhancement%20Research%20Document_.md)** - Design research and guidelines

## Quick Links

### For Developers
- [Getting Started](../README.md#getting-started)
- [Technology Stack](../README.md#technology-stack)
- [Build Instructions](../README.md#build-for-production)

### For Deployment
- [Docker Setup](./DEPLOYMENT.md#application-deployment)
- [Nginx Configuration](./DEPLOYMENT.md#nginx-reverse-proxy-configuration)
- [SSL Setup](./DEPLOYMENT.md#ssl-certificate-setup)

### For Project Management
- [Current Status](./PROJECT_STATUS.md#current-status-pre-launch-platform-ready-for-deployment)
- [Completed Features](./PROJECT_STATUS.md#completed-features)
- [Next Steps](./PROJECT_STATUS.md#next-steps-post-deployment)
