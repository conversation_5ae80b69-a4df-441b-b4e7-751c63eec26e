#!/bin/bash

# Vierla Deployment Script
# This script automates the deployment process for the Vierla application

set -e

echo "🚀 Starting Vierla deployment..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    print_error "Docker is not installed. Please install Docker first."
    exit 1
fi

# Check if Docker Compose is installed
if ! command -v docker-compose &> /dev/null; then
    print_error "Docker Compose is not installed. Please install Docker Compose first."
    exit 1
fi

# Stop existing containers
print_status "Stopping existing containers..."
docker-compose down || true

# Remove old images (optional, uncomment if you want to force rebuild)
# print_status "Removing old images..."
# docker-compose down --rmi all || true

# Pull latest changes (if in git repository)
if [ -d ".git" ]; then
    print_status "Pulling latest changes from repository..."
    git pull origin main || print_warning "Failed to pull latest changes. Continuing with current code."
fi

# Build and start containers
print_status "Building and starting containers..."
docker-compose up -d --build

# Wait for containers to be ready
print_status "Waiting for containers to be ready..."
sleep 10

# Check container status
print_status "Checking container status..."
if docker-compose ps | grep -q "Up"; then
    print_status "✅ Deployment successful! Vierla is now running."
    print_status "Application is available at: http://localhost:3000"
    print_status "Container status:"
    docker-compose ps
else
    print_error "❌ Deployment failed. Checking logs..."
    docker-compose logs
    exit 1
fi

# Show logs
print_status "Recent logs:"
docker-compose logs --tail=20

print_status "🎉 Deployment completed successfully!"
print_status "To view logs: docker-compose logs -f"
print_status "To stop: docker-compose down"
print_status "To restart: docker-compose restart"
