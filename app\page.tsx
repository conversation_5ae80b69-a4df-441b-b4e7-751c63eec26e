"use client"
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "lucide-react"
import Link from "next/link"

import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"

export default function HomePage() {
  return <GlassmorphismVariation />
}

// Glassmorphism Variation with Sage Green Theme
function GlassmorphismVariation() {
  return (
    <div className="min-h-screen relative overflow-hidden">
      {/* Professional Background with Dark Sage Green */}
      <div className="absolute inset-0" style={{background: 'linear-gradient(to bottom right, #5A7A63, #4a6b54, #5A7A63)'}}>
        <div className="absolute inset-0 animate-pulse" style={{background: 'linear-gradient(to top right, rgba(90, 122, 99, 0.3), rgba(74, 107, 84, 0.3), rgba(90, 122, 99, 0.3))'}} />

        {/* Floating Blobs */}
        {[...Array(6)].map((_, i) => (
          <div
            key={i}
            className="absolute rounded-full bg-white/10 backdrop-blur-sm animate-float"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              width: `${Math.random() * 300 + 100}px`,
              height: `${Math.random() * 300 + 100}px`,
              animationDelay: `${Math.random() * 5}s`,
              animationDuration: `${Math.random() * 10 + 15}s`,
            }}
          />
        ))}
      </div>

      {/* Header */}
      <header className="relative z-10 container mx-auto px-4 py-6">
        <nav className="flex items-center justify-between">
          <div className="flex items-center space-x-3 group">
            <div className="w-12 h-12 bg-white/20 backdrop-blur-md rounded-2xl flex items-center justify-center shadow-lg border border-white/30 transform group-hover:scale-110 transition-all duration-300">
              <Sparkles className="w-7 h-7 text-white" />
            </div>
            <div>
              <span className="text-3xl font-bold text-white drop-shadow-lg">Vierla</span>
              <div className="text-xs text-white/80 font-medium">YOUR SELF-CARE, SIMPLIFIED</div>
            </div>
          </div>
          <div className="hidden md:flex items-center space-x-8">
            <span className="text-white/60 font-medium drop-shadow-sm">
              Services (Coming Soon)
            </span>
            <Link
              href="/about"
              className="transition-all duration-300 font-medium transform hover:scale-110 drop-shadow-sm"
              style={{color: '#F5F0E1'}}
              onMouseEnter={(e) => e.target.style.color = '#FF6F61'}
              onMouseLeave={(e) => e.target.style.color = '#F5F0E1'}
            >
              About
            </Link>
            <span className="text-white/60 font-medium drop-shadow-sm">
              Contact (Coming Soon)
            </span>
          </div>
        </nav>
      </header>

      {/* Hero Section */}
      <section className="relative z-10 container mx-auto px-4 py-20">
        <div className="text-center max-w-6xl mx-auto">
          <div className="inline-flex items-center rounded-full px-6 py-3 mb-8 shadow-lg border-2 animate-bounce" style={{backgroundColor: 'rgba(245, 240, 225, 0.2)', borderColor: '#F5F0E1'}}>
            <span className="text-white font-medium drop-shadow-sm">Launching Soon</span>
          </div>

          <h1 className="text-6xl md:text-8xl font-black mb-8 leading-none text-white drop-shadow-lg" style={{fontFamily: 'Playfair Display, serif'}}>
            <span className="inline-block">Expert Beauty Services,</span>
            <br />
            <span className="inline-block" style={{color: '#F5F0E1'}}>
              Delivered to You
            </span>
          </h1>

          <p className="text-xl md:text-2xl text-white/90 mb-12 leading-relaxed font-light max-w-4xl mx-auto drop-shadow-sm">
            Book top-vetted, local hair stylists, makeup artists, and nail technicians with confidence.
            <br />
            <span className="text-white font-medium" style={{color: '#F5F0E1'}}>Your place, your time.</span>
          </p>



          {/* Dual-sided Navigation */}
          <div className="text-center">
            <p className="text-white/80 mb-4">Are you a beauty professional?</p>
            <Link
              href="/about"
              className="inline-flex items-center px-6 py-3 rounded-full border-2 text-white hover:bg-white/10 transition-all duration-300"
              style={{borderColor: '#F5F0E1', color: '#F5F0E1'}}
            >
              Learn About Joining Our Platform
            </Link>
          </div>




        </div>
      </section>

      {/* Trust Bar */}
      <section className="relative z-10 py-8 border-t border-white/20">
        <div className="container mx-auto px-4">
          <div className="text-center">
            <p className="text-white/80 text-lg mb-4">Trusted by beauty professionals and customers nationwide</p>
            <div className="flex flex-wrap justify-center items-center gap-8 text-white/60">
              <span className="text-sm">✓ Verified Professionals</span>
              <span className="text-sm">✓ Secure Payments</span>
              <span className="text-sm">✓ Satisfaction Guaranteed</span>
              <span className="text-sm">✓ Licensed & Insured</span>
            </div>
          </div>
        </div>
      </section>

      {/* How It Works Section */}
      <section className="relative z-10 py-20">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-5xl font-black text-white mb-6 drop-shadow-lg" style={{fontFamily: 'Playfair Display, serif'}}>How It Works</h2>
            <p className="text-xl text-white/90 max-w-3xl mx-auto drop-shadow-sm">
              Getting beautiful has never been this simple
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-12 max-w-5xl mx-auto">
            <div className="text-center">
              <div className="w-20 h-20 mx-auto mb-6 rounded-full flex items-center justify-center" style={{backgroundColor: 'rgba(245, 240, 225, 0.2)', border: '2px solid #F5F0E1'}}>
                <svg className="w-10 h-10" style={{color: '#F5F0E1'}} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </div>
              <h3 className="text-2xl font-bold text-white mb-4" style={{fontFamily: 'Playfair Display, serif'}}>1. Discover</h3>
              <p className="text-white/80 leading-relaxed">
                Explore our curated network of top-rated beauty professionals in your area.
              </p>
            </div>

            <div className="text-center">
              <div className="w-20 h-20 mx-auto mb-6 rounded-full flex items-center justify-center" style={{backgroundColor: 'rgba(245, 240, 225, 0.2)', border: '2px solid #F5F0E1'}}>
                <svg className="w-10 h-10" style={{color: '#F5F0E1'}} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
              </div>
              <h3 className="text-2xl font-bold text-white mb-4" style={{fontFamily: 'Playfair Display, serif'}}>2. Book</h3>
              <p className="text-white/80 leading-relaxed">
                Select your service, choose a time that works for you, and pay securely online.
              </p>
            </div>

            <div className="text-center">
              <div className="w-20 h-20 mx-auto mb-6 rounded-full flex items-center justify-center" style={{backgroundColor: 'rgba(245, 240, 225, 0.2)', border: '2px solid #F5F0E1'}}>
                <svg className="w-10 h-10" style={{color: '#F5F0E1'}} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z" />
                </svg>
              </div>
              <h3 className="text-2xl font-bold text-white mb-4" style={{fontFamily: 'Playfair Display, serif'}}>3. Relax</h3>
              <p className="text-white/80 leading-relaxed">
                Your vetted professional comes to you, ready to provide an exceptional service.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Services Section - 8 Categories */}
      <section id="services" className="relative z-10 py-20">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-5xl font-black text-white mb-6 drop-shadow-lg" style={{fontFamily: 'Playfair Display, serif'}}>Featured Services</h2>
            <p className="text-xl text-white/90 max-w-4xl mx-auto drop-shadow-sm mb-6">
              Discover our curated selection of premium beauty services, delivered by vetted professionals to your location.
            </p>
            <div className="inline-flex items-center px-6 py-3 rounded-full border-2 text-white/90" style={{borderColor: '#F5F0E1', backgroundColor: 'rgba(245, 240, 225, 0.1)'}}>
              <span className="text-sm font-medium">All services coming soon to your area</span>
            </div>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-4 gap-6 max-w-7xl mx-auto">
            {[
              {
                title: "Barbers",
                services: ["• Classic cuts", "• Beard trims", "• Hot towel shaves", "• Hair styling"],
                href: "/services/barbers",
              },
              {
                title: "Makeup",
                services: ["• Event makeup", "• Bridal looks", "• Fashion makeup", "• Everyday glam"],
                href: "/services/makeup",
              },
              {
                title: "Salons",
                services: ["• Hair cuts & color", "• Blowouts", "• Treatments", "• Full styling"],
                href: "/services/salons",
              },
              {
                title: "Locs",
                services: ["• Loc maintenance", "• Retwisting", "• Loc styling", "• Loc repair"],
                href: "/services/locs",
              },
              {
                title: "Braids",
                services: ["• Box braids", "• Cornrows", "• French braids", "• Protective styles"],
                href: "/services/braids",
              },
              {
                title: "Nails",
                services: ["• Manicures", "• Pedicures", "• Nail art", "• Gel polish"],
                href: "/services/nails",
              },
              {
                title: "Brows",
                services: ["• Eyebrow shaping", "• Threading", "• Tinting", "• Microblading"],
                href: "/services/brows",
              },
              {
                title: "Eyelashes",
                services: ["• Lash extensions", "• Lash lifts", "• Lash tinting", "• Volume lashes"],
                href: "/services/eyelashes",
              },
            ].map((service, index) => (
              <div key={index}>
                <Card className="bg-white/15 backdrop-blur-md border border-white/25 shadow-xl rounded-2xl overflow-hidden h-full">
                  <CardContent className="p-6 text-center h-full flex flex-col justify-between">
                    <div>
                      <h3 className="text-xl font-bold text-white mb-4 drop-shadow-sm" style={{fontFamily: 'Playfair Display, serif'}}>
                        {service.title}
                      </h3>
                      <div className="text-white/80 text-sm leading-relaxed drop-shadow-sm text-left">
                        {service.services.map((serviceItem, idx) => (
                          <div key={idx} className="mb-1">
                            {serviceItem}
                          </div>
                        ))}
                      </div>
                    </div>
                    <div className="mt-4">
                      <span className="text-xs px-3 py-1 rounded-full" style={{backgroundColor: 'rgba(245, 240, 225, 0.2)', color: '#F5F0E1'}}>
                        Coming Soon
                      </span>
                    </div>
                  </CardContent>
                </Card>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Service Providers Section */}
      <section className="relative z-10 py-20">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-5xl font-black text-white mb-6 drop-shadow-lg" style={{fontFamily: 'Playfair Display, serif'}}>For Beauty Professionals</h2>
            <p className="text-xl text-white/90 max-w-4xl mx-auto drop-shadow-sm mb-6">
              Join our curated network of top-tier beauty professionals. Build your business with powerful tools designed for your success.
            </p>
            <div className="inline-flex items-center px-6 py-3 rounded-full border-2 text-white/90" style={{borderColor: '#F5F0E1', backgroundColor: 'rgba(245, 240, 225, 0.1)'}}>
              <span className="text-sm font-medium">Professional applications opening soon</span>
            </div>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8 max-w-7xl mx-auto">
            {[
              {
                title: "Digital Store",
                description: "Create your own branded online presence with portfolio showcase, service listings, and customer reviews.",
                features: ["Custom branding", "Portfolio gallery", "Service catalog", "Customer reviews"]
              },
              {
                title: "Booking Management",
                description: "Centralized appointment scheduling with calendar integration, automated reminders, and easy rescheduling.",
                features: ["Calendar sync", "Auto reminders", "Easy rescheduling", "Availability control"]
              },
              {
                title: "Business Analytics",
                description: "Track your performance with detailed insights on bookings, revenue, customer behavior, and growth trends.",
                features: ["Revenue tracking", "Customer insights", "Booking analytics", "Growth metrics"]
              },
              {
                title: "Payment Processing",
                description: "Secure payment handling with multiple payment methods and direct deposits to your preferred account.",
                features: ["Multiple payment methods", "Secure transactions", "Direct deposits", "Transaction history"]
              }
            ].map((feature, index) => (
              <div
                key={index}
                className="bg-white/15 backdrop-blur-md border border-white/25 shadow-xl rounded-2xl overflow-hidden p-8"
              >
                <div className="text-center">
                  <h3 className="text-2xl font-bold text-white mb-4 drop-shadow-sm" style={{fontFamily: 'Playfair Display, serif'}}>
                    {feature.title}
                  </h3>
                  <p className="text-white/80 leading-relaxed mb-6 drop-shadow-sm">
                    {feature.description}
                  </p>
                  <div className="space-y-2">
                    {feature.features.map((item, idx) => (
                      <div key={idx} className="text-white/70 text-sm flex items-center justify-center">
                        <span className="w-2 h-2 rounded-full mr-2" style={{backgroundColor: '#F5F0E1'}}></span>
                        {item}
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            ))}
          </div>

          <div className="text-center mt-16">
            <div className="bg-white/20 backdrop-blur-md rounded-3xl p-12 shadow-2xl max-w-4xl mx-auto border border-white/30">
              <h3 className="text-4xl font-black text-white mb-6 drop-shadow-lg" style={{fontFamily: 'Playfair Display, serif'}}>Coming Soon</h3>
              <p className="text-xl text-white/90 mb-8 max-w-2xl mx-auto drop-shadow-sm">
                We're building something special for beauty professionals. Join our waitlist to be the first to know when we launch.
              </p>
              <div className="grid md:grid-cols-2 gap-6 max-w-2xl mx-auto">
                <div className="text-center p-4 rounded-xl" style={{backgroundColor: 'rgba(245, 240, 225, 0.1)', border: '1px solid #F5F0E1'}}>
                  <h4 className="text-lg font-semibold text-white mb-2">iOS App</h4>
                  <p className="text-white/80 text-sm">Coming to the App Store</p>
                </div>
                <div className="text-center p-4 rounded-xl" style={{backgroundColor: 'rgba(245, 240, 225, 0.1)', border: '1px solid #F5F0E1'}}>
                  <h4 className="text-lg font-semibold text-white mb-2">Android App</h4>
                  <p className="text-white/80 text-sm">Coming to Google Play</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>



      {/* Footer */}
      <footer className="relative z-10 text-white py-16 border-t" style={{backgroundColor: 'rgba(90, 122, 99, 0.8)', borderColor: '#F5F0E1'}}>
        <div className="container mx-auto px-4">
          <div className="grid md:grid-cols-4 gap-12">
            <div>
              <div className="flex items-center space-x-3 mb-6">
                <div className="w-10 h-10 rounded-2xl flex items-center justify-center border-2" style={{backgroundColor: 'rgba(245, 240, 225, 0.2)', borderColor: '#F5F0E1'}}>
                  <Sparkles className="w-6 h-6 drop-shadow-sm" style={{color: '#F5F0E1'}} />
                </div>
                <span className="text-3xl font-bold drop-shadow-lg" style={{fontFamily: 'Playfair Display, serif'}}>Vierla</span>
              </div>
              <p className="text-white/80 drop-shadow-sm leading-relaxed">
                Expert beauty services, delivered to you. Connecting top-vetted professionals with discerning customers for an exceptional experience.
              </p>
            </div>

            <div>
              <h4 className="font-bold mb-6 text-xl text-white drop-shadow-sm" style={{fontFamily: 'Playfair Display, serif'}}>Services</h4>
              <ul className="space-y-3 text-white/80">
                <li>Barbers</li>
                <li>Makeup Artists</li>
                <li>Hair Stylists</li>
                <li>Locs Specialists</li>
              </ul>
            </div>

            <div>
              <h4 className="font-bold mb-6 text-xl text-white drop-shadow-sm" style={{fontFamily: 'Playfair Display, serif'}}>More Services</h4>
              <ul className="space-y-3 text-white/80">
                <li>Braiding</li>
                <li>Nail Technicians</li>
                <li>Brow Artists</li>
                <li>Lash Specialists</li>
              </ul>
            </div>

            <div>
              <h4 className="font-bold mb-6 text-xl text-white drop-shadow-sm" style={{fontFamily: 'Playfair Display, serif'}}>Company</h4>
              <ul className="space-y-3 text-white/80">
                <li>
                  <Link href="/about" className="transition-colors" style={{color: '#F5F0E1'}} onMouseEnter={(e) => e.target.style.color = '#FF6F61'} onMouseLeave={(e) => e.target.style.color = '#F5F0E1'}>
                    About Us
                  </Link>
                </li>
                <li>Contact Us (Coming Soon)</li>
                <li>Privacy Policy (Coming Soon)</li>
                <li>Terms of Service (Coming Soon)</li>
              </ul>
            </div>
          </div>

          <div className="mt-12 pt-8 text-center" style={{borderTop: '1px solid #F5F0E1'}}>
            <p className="text-white/80 drop-shadow-sm">
              &copy; 2024 Vierla - Expert Beauty Services, Delivered to You. All rights reserved.
            </p>
          </div>
        </div>
      </footer>

      <style jsx>{`
        @keyframes float {
          0%, 100% { transform: translateY(0px) rotate(0deg); }
          25% { transform: translateY(-20px) rotate(2deg); }
          50% { transform: translateY(-40px) rotate(0deg); }
          75% { transform: translateY(-20px) rotate(-2deg); }
        }
        .animate-float {
          animation: float 20s ease-in-out infinite;
        }
      `}</style>
    </div>
  )
}

